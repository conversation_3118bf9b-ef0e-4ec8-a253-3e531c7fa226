image: docker:latest # Use a Docker image that has Docker installed

services:
  - docker:dind # Docker-in-Docker service to build images

variables:
  # Define variables for your container registry
  # Replace with your actual registry URL
  CONTAINER_REGISTRY: your.container.registry.com 
  FRONTEND_IMAGE_TAG: $CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA
  BACKEND_IMAGE_TAG: $CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA
  # VITE_API_BASE_URL will be set per environment or in GitLab CI/CD settings
  # For example, in GitLab CI/CD Variables:
  # VITE_API_BASE_URL_PRODUCTION: "https://api.yourdomain.com"
  # VITE_API_BASE_URL_DEVELOPMENT: "https://dev-api.yourdomain.com"

stages:
  - build
  - deploy

before_script:
  # Login to your container registry
  # Ensure DOCKER_USER and DOCKER_PASSWORD are set as secret CI/CD variables in GitLab
  - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USER" --password-stdin $CONTAINER_REGISTRY
  # Or for <PERSON><PERSON><PERSON><PERSON>'s own registry:
  # - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

build_frontend:
  stage: build
  script:
    - echo "Building frontend image..."
    - docker build -t $CONTAINER_REGISTRY/your-repo/frontend:$FRONTEND_IMAGE_TAG -f frontend/Dockerfile ./frontend
    - docker push $CONTAINER_REGISTRY/your-repo/frontend:$FRONTEND_IMAGE_TAG
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - frontend/**/*

build_backend:
  stage: build
  script:
    - echo "Building backend image..."
    - docker build -t $CONTAINER_REGISTRY/your-repo/backend:$BACKEND_IMAGE_TAG -f backend/Dockerfile ./backend
    - docker push $CONTAINER_REGISTRY/your-repo/backend:$BACKEND_IMAGE_TAG
  rules:
    - if: $CI_COMMIT_BRANCH
      changes:
        - backend/**/*

# --- DEPLOYMENT STAGES ---
# These are conceptual. Actual deployment scripts will vary greatly.

deploy_dev_frontend:
  stage: deploy
  variables:
    # This VITE_API_BASE_URL is passed as a build-arg to the frontend Docker build
    # if the Dockerfile is set up to receive it and bake it into the static assets.
    # Alternatively, it can be injected at runtime via environment variables if the entrypoint script handles it.
    # For Vite, baking it in at build time is common for VITE_ prefixed vars.
    # This requires modifying the frontend Dockerfile to accept VITE_API_BASE_URL as a build arg
    # and then using it to replace a placeholder or set an env var before `vite build`.
    # A simpler approach for runtime config is to serve a config.js file via Nginx that Vue fetches.
    # For this example, we'll assume it's handled by the deployment environment.
    EFFECTIVE_VITE_API_BASE_URL: $VITE_API_BASE_URL_DEVELOPMENT # From GitLab CI/CD Variables
  script:
    - echo "Deploying frontend to DEV environment..."
    - echo "Image:$CONTAINER_REGISTRY/your-repo/frontend:$FRONTEND_IMAGE_TAG"
    - echo "API URL for DEV:$EFFECTIVE_VITE_API_BASE_URL"
    # Add your actual deployment commands here (e.g., kubectl apply, helm upgrade, ssh script)
    # Example: ssh deploy@dev-server "deploy_script.sh $CONTAINER_REGISTRY/your-repo/frontend:$FRONTEND_IMAGE_TAG $EFFECTIVE_VITE_API_BASE_URL"
  environment:
    name: development
    url: http://dev.your-app.com # Replace with your dev frontend URL
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" # Or your development branch
      changes:
        - frontend/**/* # Only run if frontend changed

deploy_dev_backend:
  stage: deploy
  script:
    - echo "Deploying backend to DEV environment..."
    - echo "Image:$CONTAINER_REGISTRY/your-repo/backend:$BACKEND_IMAGE_TAG"
    # Add your actual deployment commands here
  environment:
    name: development
  rules:
    - if: $CI_COMMIT_BRANCH == "develop" # Or your development branch
      changes:
        - backend/**/* # Only run if backend changed

deploy_prod_frontend:
  stage: deploy
  variables:
    EFFECTIVE_VITE_API_BASE_URL: $VITE_API_BASE_URL_PRODUCTION # From GitLab CI/CD Variables
  script:
    - echo "Deploying frontend to PROD environment..."
    - echo "Image:$CONTAINER_REGISTRY/your-repo/frontend:$FRONTEND_IMAGE_TAG"
    - echo "API URL for PROD:$EFFECTIVE_VITE_API_BASE_URL"
    # Add your actual deployment commands here
  environment:
    name: production
    url: http://your-app.com # Replace with your prod frontend URL
  rules:
    - if: $CI_COMMIT_BRANCH == "main" # Or your production branch
      changes:
        - frontend/**/*
      when: manual # Optional: make production deployments manual

deploy_prod_backend:
  stage: deploy
  script:
    - echo "Deploying backend to PROD environment..."
    - echo "Image:$CONTAINER_REGISTRY/your-repo/backend:$BACKEND_IMAGE_TAG"
    # Add your actual deployment commands here
  environment:
    name: production
  rules:
    - if: $CI_COMMIT_BRANCH == "main" # Or your production branch
      changes:
        - backend/**/*
      when: manual # Optional: make production deployments manual

after_script:
  - docker logout $CONTAINER_REGISTRY # Logout from registry