# 🌩️ Aiven Cloud PostgreSQL Configuration
# Deconstructed from: postgres://avnadmin:<EMAIL>:11371/defaultdb?sslmode=require

# Database Configuration
DB_HOST=pg-27bc16ff-dimas-13.c.aivencloud.com
DB_PORT=11371
DB_USER=avnadmin
DB_PASS=AVNS_jUxntoS32yeExaHOO-L
DB_NAME=csrit_backend

# SSL Configuration (Required for Aiven)
DB_SSL=true
DB_SSL_REJECT_UNAUTHORIZED=false

# Performance Configuration
DB_POOL_MIN=3
DB_POOL_MAX=15
DB_QUERY_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000

# Cache Configuration (if you have Redis)
REDIS_HOST=localhost
REDIS_PORT=6379
CACHE_TTL=300

# Application Configuration
NODE_ENV=development
APP_PORT=3000

# Security Configuration
JWT_ACCESS_TOKEN_SECRET=your_jwt_secret_change_this
JWT_ACCESS_TOKEN_EXPIRATION_TIME=15m
JWT_REFRESH_TOKEN_SECRET=your_refresh_secret_change_this
JWT_REFRESH_TOKEN_EXPIRATION_TIME=7d
JWT_ACCESS_COOKIE=true

# Monitoring Configuration
ENABLE_QUERY_LOGGING=true
SLOW_QUERY_THRESHOLD=1000

# Cloud-specific optimizations
DB_CONNECTION_RETRY_ATTEMPTS=3
DB_CONNECTION_RETRY_DELAY=1000
