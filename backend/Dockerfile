# Stage 1: Build the application
FROM node:18-alpine AS build-stage

WORKDIR /usr/src/app

# Copy package.json and pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm

# Install dependencies using pnpm
# Use --prod for production dependencies if you want to separate,
# but for NestJS builds, devDependencies are often needed for compilation.
# Using --frozen-lockfile is good practice.
RUN pnpm install --frozen-lockfile

# Copy the rest of the application code
COPY . .

# Build the application (NestJS typically uses 'nest build')
# This script should be in backend/package.json, e.g., "build": "nest build"
RUN pnpm run build

# Stage 2: Production environment
FROM node:18-alpine AS production-stage

WORKDIR /usr/src/app

# Copy package.json and pnpm-lock.yaml for installing production dependencies
COPY package.json pnpm-lock.yaml ./

# Install pnpm
RUN npm install -g pnpm

# Install only production dependencies
RUN pnpm install --prod --frozen-lockfile

# Copy the built application (dist folder) and other necessary files from the build stage
COPY --from=build-stage /usr/src/app/dist ./dist
# If your NestJS app has other assets that need to be copied to the production image (e.g., .env files, templates), add them here.
# For example, if you have a 'public' or 'views' folder:
# COPY --from=build-stage /usr/src/app/public ./public
# COPY --from=build-stage /usr/src/app/views ./views

# Expose the port the app runs on (default for NestJS is 3000)
EXPOSE 3000

# Command to run the application
# This script should be in backend/package.json, e.g., "start:prod": "node dist/main"
CMD ["pnpm", "run", "start:prod"]
