import {
  Controller,
  Get,
  Post,
  Body,
  Param,
} from '@nestjs/common';
import { BulanTahunService } from './bulan_tahun.service';
import {
  CreateBulanTahunDto,
  CreateTahunTahunDto,
} from './dto/create-bulan_tahun.dto';
import { ApiBody, ApiTags } from '@nestjs/swagger';

@ApiTags('Modul Bulan Tahun')
@Controller('api/v1')
export class BulanTahunController {
  constructor(private readonly bulanTahunService: BulanTahunService) {}

  @Post('bulan')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama_bulan: { type: 'string' },
      },
    },
  })
  async create(@Body() createBulanTahunDto: CreateBulanTahunDto) {
    const data = await this.bulanTahunService.create(createBulanTahunDto);
    return {
      success: true,
      message: 'Data Bulan Berhasil Di Tambah',
    };
  }

  @Get('bulan')
  async findAll() {
    const data = await this.bulanTahunService.findAll();
    return {
      success: true,
      message: 'Data Bulan',
      data: data,
    };
  }

  @Get('bulan/:id')
  async findOne(@Param('id') id: string) {
    const data = await this.bulanTahunService.findOne(id);
    return {
      success: true,
      message: 'Data Bulan By ID',
      data: data,
    };
  }

  @Post('bulan/:id')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama_bulan: { type: 'string' },
      },
    },
  })
  async update(@Param('id') id: string, @Body() dataP: CreateBulanTahunDto) {
    const data = await this.bulanTahunService.update(id, dataP);
    return {
      success: true,
      message: 'Data Bulan Berhasil Di Perbaharui',
    };
  }

  @Post('bulan/delete/:id')
  async remove(@Param('id') id: string) {
    const data = await this.bulanTahunService.remove(id);
    return {
      success: true,
      message: 'Data Bulan Berhasil Di Hapus',
    };
  }

  @Post('tahun')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama_tahun: { type: 'string' },
      },
    },
  })
  async createTahun(@Body() dataP: CreateTahunTahunDto) {
    const data = await this.bulanTahunService.createTahun(dataP);
    return {
      success: true,
      message: 'Data Bulan Berhasil Di Tahun',
    };
  }

  @Get('tahun')
  async findAllTahun() {
    const data = await this.bulanTahunService.findAllTahun();
    return {
      success: true,
      message: 'Data Tahun',
      data: data,
    };
  }

  @Get('tahun/:id')
  async findOneTahun(@Param('id') id: string) {
    const data = await this.bulanTahunService.findOneTahun(id);
    return {
      success: true,
      message: 'Data Tahun By ID',
      data: data,
    };
  }

  @Post('tahun/:id')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama_tahun: { type: 'string' },
      },
    },
  })
  async updateTahun(
    @Param('id') id: string,
    @Body() dataP: CreateBulanTahunDto,
  ) {
    const data = await this.bulanTahunService.updateTahun(id, dataP);
    return {
      success: true,
      message: 'Data Tahun Berhasil Di Perbaharui',
    };
  }

  @Post('tahun/delete/:id')
  async removeTahun(@Param('id') id: string) {
    const data = await this.bulanTahunService.removeTahun(id);
    return {
      success: true,
      message: 'Data Tahun Berhasil Di Hapus',
    };
  }
}
