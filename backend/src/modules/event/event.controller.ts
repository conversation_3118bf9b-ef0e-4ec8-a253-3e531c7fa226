import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Request,
  UploadedFiles,
  UseInterceptors,
  BadRequestException,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiTags } from '@nestjs/swagger';
import * as fs from 'fs';
import { createWriteStream } from 'fs';
import { Public } from 'src/common/decorators/public.decorator';
import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';
import { CreateEventDto } from './dto/create-event.dto';
import { EventService } from './event.service';
import { StorageConfig } from 'src/config/storage.config';

@Controller('api/v1/event')
@ApiTags('Modul Event')
export class EventController {
  constructor(
    private readonly eventService: EventService,
    private readonly fileService: FileService,
  ) {}

  @Post()
  @Public()
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  async newControllerPost(@UploadedFiles() files, @Body() dto: CreateEventDto) {
    var dataFileBase;

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      dataFileBase = files.map((file, i) => {
        // Validate file size
        if (file.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            `File ${file.originalname} terlalu besar. Maksimal 10MB`,
          );
        }

        // Validate file extension
        const extension = file.originalname.substring(
          file.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException(
            `Tipe file ${file.originalname} tidak diizinkan`,
          );
        }

        // Generate secure filename
        const namaFile = StorageConfig.generateSecureFilename(
          file.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(file.buffer);

        return namaFile;
      });
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file event');
    }

    const data = await this.eventService.create(dto, dataFileBase);
    return {
      success: true,
      message: 'Data Event Berhasil Di Tambah',
    };
  }

  @Get()
  async findAll() {
    const data = await this.eventService.findAll();
    return {
      success: true,
      message: 'Data Event',
      data: data,
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.eventService.findOne(id);
    return {
      success: true,
      message: 'Data Event By ID',
      data: data,
    };
  }

  // @Public()
  @Get(':id/:name')
  async deleteFile(@Param('id') id: string, @Param('name') name: string) {
    const data = await this.eventService.deleteFileOne(id, name);
    return {
      success: true,
      message: 'Data Event Berhasil Di Hapus',
    };
  }

  @Post(':id')
  @Public()
  @UseInterceptors(FilesInterceptor('files'))
  @ApiConsumes('multipart/form-data')
  async update(
    @Param('id') id: string,
    @UploadedFiles() files,
    @Body() dto: CreateEventDto,
  ) {
    let existingEvent = await this.eventService.findOne(id);
    let dataFileBase = existingEvent.files || [];

    try {
      // Ensure secure upload directory exists
      if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
        fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
      }

      if (files && files.length) {
        const newFiles = files.map((file, i) => {
          // Validate file size
          if (file.size > StorageConfig.MAX_FILE_SIZE) {
            throw new BadRequestException(
              `File ${file.originalname} terlalu besar. Maksimal 10MB`,
            );
          }

          // Validate file extension
          const extension = file.originalname.substring(
            file.originalname.lastIndexOf('.'),
          );
          if (
            !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
          ) {
            throw new BadRequestException(
              `Tipe file ${file.originalname} tidak diizinkan`,
            );
          }

          // Generate secure filename
          const namaFile = StorageConfig.generateSecureFilename(
            file.originalname,
          );
          const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

          // Write file to secure location
          const ws = createWriteStream(secureFilePath);
          ws.write(file.buffer);

          return namaFile;
        });

        dataFileBase = [...dataFileBase, ...newFiles];
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file event');
    }

    await this.eventService.update(id, dto, dataFileBase);
    return {
      success: true,
      message: 'Data Event Berhasil Di Update',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string, @Request() req) {
    const data = await this.eventService.remove(id, req.headers.id_user);
    return {
      success: true,
      message: 'Data Event Berhasil Di Hapus',
    };
  }
}
