import {
  Controller,
  Get,
  Post,
  Body,
  Param,
} from '@nestjs/common';
import { KontakService } from './kontak.service';
import { CreateKontakDto } from './dto/create-kontak.dto';
import { UpdateKontakDto } from './dto/update-kontak.dto';
import { ApiTags } from '@nestjs/swagger';

@Controller('api/v1/kontak')
@ApiTags('Modul Kontak')
export class KontakController {
  constructor(private readonly kontakService: KontakService) {}

  @Post()
  async create(@Body() createKontakDto: CreateKontakDto) {
    const data = await this.kontakService.create(createKontakDto);
    return {
      success: true,
      message: 'Data Kontak Berhasil Dibuat',
    };
  }

  @Get()
  async findAll() {
    const data = await this.kontakService.findAll();
    return {
      data: data,
      success: true,
      message: 'Data Kontak',
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.kontakService.findOne(id);
    return {
      data: data,
      success: true,
      message: 'Data Kontak By ID',
    };
  }

  @Post(':id')
  async update(
    @Param('id') id: string,
    @Body() updateKontakDto: UpdateKontakDto,
  ) {
    const data = await this.kontakService.update(id, updateKontakDto);
    return {
      success: true,
      message: 'Data Kontak Berhasil Diupdate',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string) {
    await this.kontakService.remove(id);
    return {
      success: true,
      message: 'Data Kontak Berhasil Di Hapus',
    };
  }
}
