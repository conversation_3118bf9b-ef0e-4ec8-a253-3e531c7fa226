import {
  Controller,
  Request,
  Get,
  Post,
  Body,
  Param,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { LayananService } from './layanan.service';
import { CreateLayananDto } from './dto/create-layanan.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import * as fs from 'fs';
import { createWriteStream } from 'fs';
import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';
import { StorageConfig } from 'src/config/storage.config';

@Controller('api/v1/layanan')
@ApiTags('Modul Layanan')
export class LayananController {
  constructor(
    private readonly layananService: LayananService,
    private readonly fileService: FileService,
  ) {}

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        kategori: { type: 'string' },
        isi: { type: 'text' },
        tgl_posting: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('foto'))
  @Post()
  async create(
    @Body() dataPost: CreateLayananDto,
    @UploadedFile() foto: Express.Multer.File,
    @Request() req,
  ) {
    let namaFile;

    try {
      if (foto) {
        // Validate file size
        if (foto.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = foto.originalname.substring(
          foto.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(foto.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(foto.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(foto.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file layanan');
    }

    const data = await this.layananService.create(
      dataPost,
      namaFile,
      req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data Layanan Berhasil Di Tambah',
    };
  }

  @Get()
  async findAll() {
    const data = await this.layananService.findAll();
    return {
      success: true,
      message: 'Data Layanan',
      data: data,
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.layananService.findOne(id);
    return {
      success: true,
      message: 'Data Layanan By ID',
      data: data,
    };
  }

  @Post(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        judul_en: { type: 'string' },
        kategori: { type: 'string' },
        kategori_en: { type: 'string' },
        isi: { type: 'text' },
        tgl_posting: { type: 'text' },
        foto: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('foto'))
  async update(
    @Param('id') id: string,
    @Body() dataPost: CreateLayananDto,
    @UploadedFile() foto: Express.Multer.File,
    @Request() req,
  ) {
    let namaFile;

    try {
      if (foto) {
        // Validate file size
        if (foto.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = foto.originalname.substring(
          foto.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(foto.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(foto.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(foto.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file layanan');
    }

    const data = await this.layananService.update(
      id,
      dataPost,
      namaFile,
      req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data Layanan Berhasil Di Update',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string, @Request() req) {
    const data = await this.layananService.remove(id, req.headers.id_user);
    return {
      success: true,
      message: 'Data Layanan Berhasil Di Hapus',
    };
  }
}
