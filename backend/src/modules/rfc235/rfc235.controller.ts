import {
  Controller,
  Request,
  Get,
  Post,
  Body,
  Param,
  UseInterceptors,
  UploadedFile,
  Res,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { Rfc235Service } from './rfc235.service';
import { CreateRfc235Dto } from './dto/create-rfc235.dto';
import { UpdateRfc235Dto } from './dto/update-rfc235.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { createWriteStream } from 'fs';
import * as fs from 'fs';

import { join } from 'path';
import type { Response } from 'express';
import { FileService } from 'src/utils/globalFunction/FileRenameWithTimestamp';
import { FileAccessService } from 'src/common/services/file-access.service';
import { SecureFileEndpoint } from 'src/common/decorators/secure-file-endpoint.decorator';
import { StorageConfig } from 'src/config/storage.config';
import { AuditService } from 'src/common/services/audit.service';

@Controller('api/v1/rfc235')
@ApiTags('Modul Rfc235')
export class Rfc235Controller {
  constructor(
    private readonly rfc235Service: Rfc235Service,
    private readonly fileService: FileService,
    private readonly fileAccessService: FileAccessService,
    private readonly auditService: AuditService,
  ) {}

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        kategori: { type: 'text' },
        judul: { type: 'text' },
        isi: { type: 'text' },
        tgl_posting: { type: 'text' },
        file_pdf: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file_pdf'))
  @Post()
  async create(
    @Body() data: CreateRfc235Dto,
    @UploadedFile() file_pdf: Express.Multer.File,
    @Request() req,
  ) {
    let namaFile;

    try {
      if (file_pdf) {
        // Validate file size
        if (file_pdf.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException('File terlalu besar. Maksimal 10MB');
        }

        // Validate file extension
        const extension = file_pdf.originalname.substring(
          file_pdf.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file tidak diizinkan');
        }

        // Validate MIME type
        const allowedMimes = [
          'image/jpeg',
          'image/jpg',
          'image/png',
          'application/pdf',
        ];
        if (!allowedMimes.includes(file_pdf.mimetype)) {
          throw new BadRequestException('Tipe file tidak valid');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(file_pdf.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(file_pdf.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file RFC');
    }

    const dataa = await this.rfc235Service.create(data, namaFile);
    return {
      success: true,
      message: 'Data File Berhasil Di Tambah',
    };
  }

  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        kategori: { type: 'text' },
      },
    },
  })
  @Post('kategori')
  async getByLanguage(@Body() body: any) {
    const data = await this.rfc235Service.getByLanguage(body);
    return {
      success: true,
      message: 'Data RFC',
      data: data,
    };
  }

  @Get()
  async findAll() {
    const data = await this.rfc235Service.findAll();
    return {
      success: true,
      message: 'Data RFC',
      data: data,
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.rfc235Service.findOne(id);

    return {
      success: true,
      message: 'Data RFC By ID',
      data: data,
    };
  }

  @Get('base64/:id')
  @SecureFileEndpoint()
  async findOneToBase64(
    @Param('id') id: string,
    @Res() res: Response,
    @Request() req,
  ) {
    try {
      // Check if user has access to this file
      const hasAccess = await this.rfc235Service.checkFileAccess(
        id,
        req.user.id,
      );
      if (!hasAccess) {
        // Log failed access attempt
        await this.auditService.logFailedAccess(
          req.user.id,
          id,
          'download',
          req.ip,
          'Access denied - insufficient permissions',
        );
        throw new ForbiddenException('Access denied to this file');
      }

      // Get file data
      const data = await this.rfc235Service.findOne(id);
      const filePath = join('./uploads/rfc/', data.file_pdf);

      // Use the secure file access service to serve the file
      await this.fileAccessService.serveFile(filePath, res, {
        contentType: 'application/pdf',
        filename: `csirs_file_${data.file_pdf}`,
        inline: false,
      });

      // Log successful file access
      await this.auditService.logSuccessfulAccess(
        req.user.id,
        id,
        'download',
        req.ip,
        `RFC file downloaded: ${data.file_pdf}`,
      );
    } catch (error) {
      if (error instanceof ForbiddenException) {
        // Already logged above, just re-throw
        throw error;
      } else {
        // Log other errors
        await this.auditService.logFailedAccess(
          req.user?.id || 'unknown',
          id,
          'download',
          req.ip,
          `Error: ${error.message}`,
        );
        throw error;
      }
    }
  }

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        kategori: { type: 'text' },
        judul: { type: 'text' },
        tgl_post: { type: 'text' },
        file_pdf: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file_pdf'))
  @Post(':id')
  async update(
    @Param('id') id: string,
    @Body() data: UpdateRfc235Dto,
    @UploadedFile() file_pdf: Express.Multer.File,
    @Request() req,
  ) {
    let namaFile;

    try {
      if (file_pdf) {
        // Validate file size
        if (file_pdf.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException('File terlalu besar. Maksimal 10MB');
        }

        // Validate file extension
        const extension = file_pdf.originalname.substring(
          file_pdf.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (file_pdf.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(file_pdf.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(file_pdf.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Error saat menyimpan file RFC');
    }

    const dataa = await this.rfc235Service.update(
      id,
      data,
      namaFile,
      req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data RFC Berhasil Di Update',
    };
  }

  @Post('delete/:id')
  remove(@Param('id') id: string) {
    return this.rfc235Service.remove(id);
  }
}
