import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  ForbiddenException,
  HttpCode,
  HttpStatus,
  Query,
  Res,
  UseGuards,
  Request,
  Response,
  UseInterceptors,
  UploadedFile,
  Req,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { createWriteStream } from 'fs';
import { GetCurrentUsername, Public } from 'src/common/decorators';
import JwtRefreshGuard from 'src/common/guards/jwt-refresh.guard';
import { isCookieJwt } from 'src/utils';
import { CreateUserDto } from '../user/dto/create-user.dto';
import { UserService } from '../user/user.service';
import { AuthService } from './auth.service';

import { ResetPasswordByUserDto } from './dto/reset-password-by-user.dto';
import { VerifyCaptchaDto } from './dto/verify-captcha.dto';
import { IsRole } from 'src/common/decorators/role.decorator';
import * as svgCaptcha from 'svg-captcha';
import { JwtService } from '@nestjs/jwt';
import { StorageConfig } from 'src/config/storage.config';
import * as fs from 'fs';

@ApiTags('Modul Autentikasi')
@Controller('api/v1/auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}
  @Public()
  @Get('captcha')
  async generateCaptcha(@Req() req, @Res() res) {
    const captcha = svgCaptcha.create();
    let sessionId = req.cookies['session_id'];

    if (!sessionId) {
      sessionId = this.authService.generateNewSessionId();
      res.cookie('session_id', sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 5 * 60 * 1000, // 5 minutes
      });
    }

    // Store captcha in session service
    await this.authService.storeCaptcha(sessionId, captcha.text);

    res.status(200).json({
      captchaSvg: captcha.data,
      sessionId: sessionId,
    });
  }

  @Post('captcha/verify')
  @Public()
  @ApiConsumes('application/json')
  async verifyCaptcha(@Body() verifyCaptchaDto: VerifyCaptchaDto, @Req() req) {
    const sessionId = req.cookies['session_id'];

    if (!sessionId) {
      return { success: false, message: 'No session found' };
    }

    const isValid = await this.authService.verifyCaptcha(
      sessionId,
      verifyCaptchaDto.user_input,
    );

    return { success: isValid };
  }

  @Public()
  @Post('login')
  // @UseGuards(LocalAuthGuard)
  @ApiConsumes('application/json')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        username: { type: 'string' },
        password: { type: 'string' },
        user_input: { type: 'string' },
      },
    },
  })
  async login(@Request() req, @Res() res) {
    const user = await this.authService.checkuserauth(
      req.body.username,
      req.body.password,
    );

    try {
      if (await this.authService.checkUserDisable(req.body.username)) {
        return res.status(HttpStatus.ACCEPTED).json({
          status: false,
          message:
            'Pengguna dimatikan sementara dengan alasan kesalahan pasword berturut-turut',
        });
      }

      if (await this.authService.checkUserLogin(req.body.username)) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          status: false,
          message: 'User sedang login',
        });
      }
      console.log(user);

      const result = await this.authService.login(user, req.body.password);

      if (isCookieJwt()) {
        const valueAccessToken =
          await this.authService.getCookieWithJwtAccessToken(
            result.token.access_token,
          );

        await this.authService.saveSessionLogin(req.body.username);

        const valueRefreshToken = this.authService.getCookieWithJwtRefreshToken(
          result.token.refresh_token,
        );

        req.res.setHeader('Set-Cookie', [valueAccessToken, valueRefreshToken]);

        return res.status(HttpStatus.ACCEPTED).json({
          status: true,
          data: result,
        });
      }
      return res.status(HttpStatus.ACCEPTED).json({
        status: false,
        data: result,
      });
    } catch (error) {
      return res.status(HttpStatus.ACCEPTED).json({
        status: false,
        data: '',
        message: 'error',
      });
    }
  }

  @IsRole('admin')
  @Post('register')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama: { type: 'string' },
        username: { type: 'string' },
        password: { type: 'string' },
        roleId: { type: 'string' },
        email: { type: 'string' },
        foto: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('foto'))
  async create(
    @Body() createUserDto: CreateUserDto,
    @UploadedFile() foto: Express.Multer.File,
    @Res() res,
  ) {
    try {
      if (!this.authService.isStrongPassword(createUserDto.password)) {
        return res.status(HttpStatus.FORBIDDEN).json({
          status: false,
          message:
            'Not Strong Password, Description : At least 8 characters, At least one lowercase letter, At least one uppercase letter, At least one special character (add more if needed)',
        });
      }

      let namaFile;

      if (foto) {
        // Validate file size
        if (foto.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = foto.originalname.substring(
          foto.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(foto.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(foto.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(foto.buffer);
      }

      const result = await this.authService.create(createUserDto, namaFile);
      return res.status(HttpStatus.OK).json({
        status: true,
        data: result,
      });
    } catch (error) {
      if (error instanceof BadRequestException) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          status: false,
          message: error.message,
        });
      }
      return res.status(HttpStatus.BAD_REQUEST).json({
        status: false,
        message: 'error',
      });
    }
  }

  @Public()
  @Get('confirm')
  // @Render('auth/reset-password')
  @HttpCode(HttpStatus.OK)
  async confirm(@Res() res, @Query('token') token: string) {
    if (!token) {
      throw new ForbiddenException('Token tidak ditemukan');
    }
    const user = await this.userService.find({ forgetPasswordToken: token });
    return res.status(HttpStatus.OK).json({
      status: true,
      data: user,
    });
  }

  @Get('me')
  @IsRole('admin')
  @HttpCode(HttpStatus.OK)
  async testingRole(@Res() res) {
    return res.status(HttpStatus.OK).json({
      status: true,
    });
  }

  @IsRole('admin')
  @IsRole('user')
  @Post('reset-password/:id')
  @HttpCode(HttpStatus.OK)
  async resetPassword(@Body() body, @Param('id') id: string, @Res() res) {
    if (!this.authService.isStrongPassword(body.password)) {
      return res.status(HttpStatus.FORBIDDEN).json({
        status: false,
        message:
          'Not Strong Password, Description : At least 8 characters, At least one lowercase letter, At least one uppercase letter, At least one special character (add more if needed)',
      });
    }
    await this.authService.resetPassword(id, body);
    return res.status(HttpStatus.OK).json({
      status: true,
      message: 'Berhasil memperbaharui username dan password',
    });
  }

  @IsRole('admin')
  @IsRole('user')
  @Post('reset-password-by-user')
  @HttpCode(HttpStatus.OK)
  async resetPasswordByUser(
    @GetCurrentUsername() username: string,
    @Body() resetPasswordByUser: ResetPasswordByUserDto,
    @Res() res,
  ) {
    if (!this.authService.isStrongPassword(resetPasswordByUser.password_baru)) {
      return res.status(HttpStatus.OK).json({
        status: true,
        message: 'Success',
      });
    }
    if (
      resetPasswordByUser.password_baru !=
      resetPasswordByUser.password_baru_confirm
    ) {
      return res.status(HttpStatus.OK).json({
        status: true,
        message: 'Success',
      });
    }
    const data = await this.authService.resetPasswordByUser(
      username,
      resetPasswordByUser,
    );
    return res.status(HttpStatus.OK).json({
      data,
    });
  }

  @Post('logout/:id')
  @Public()
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        username: { type: 'string' },
      },
    },
  })
  async logout(@Param('id') id: string, @Response() res, @Request() req) {
    res.setHeader('Set-Cookie', this.authService.getCookieForLogOut());

    const result = await this.authService.removeRefreshToken(id);
    await this.authService.logoutWithSession(req.body.username);
    res.clearCookie('session_id');
    const newSessionId = this.authService.generateNewSessionId();
    res.cookie('session_id', newSessionId, this.authService.getCookieOptions());
    return res.status(HttpStatus.OK).json({
      status: result,
    });
  }

  @Public()
  @UseGuards(JwtRefreshGuard)
  @Post('refresh/:id')
  @ApiConsumes('application/json')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        refresh_token: { type: 'string' },
      },
    },
  })
  @HttpCode(HttpStatus.OK)
  async refreshToken(@Param('id') id: string, @Request() req) {
    const result = await this.authService.refreshToken(
      id,
      req.body.refresh_token,
    );
    if (isCookieJwt()) {
      const { access_token } = result.token;
      const accessTokenCookie =
        this.authService.getCookieWithJwtAccessToken(access_token);
      req.res.setHeader('Set-Cookie', accessTokenCookie);
      return result.user;
    }

    delete result.token.refresh_token;

    return result;
  }
}
