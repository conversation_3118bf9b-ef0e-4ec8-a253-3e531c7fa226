import {
  Controller,
  Get,
  Post,
  Body,
  Request,
  Param,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
} from '@nestjs/common';
import { PanduanPedomanService } from './panduan-pedoman.service';
import { CreatePanduanPedomanDto } from './dto/create-panduan-pedoman.dto';
import { UpdatePanduanPedomanDto } from './dto/update-panduan-pedoman.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import * as fs from 'fs';
import { StorageConfig } from 'src/config/storage.config';
import { createWriteStream } from 'fs';

@Controller('api/v1/panduan-pedoman')
@ApiTags('Modul Panduan Pedoman')
export class PanduanPedomanController {
  constructor(private readonly panduanPedomanService: PanduanPedomanService) {}

  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        kategori: { type: 'string' },
        // isi: { type: 'text' },
        tanggal: { type: 'text' },
        file_pdf: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file_pdf'))
  @Post()
  async create(
    @Body() createPanduanPedomanDto: CreatePanduanPedomanDto,
    @UploadedFile() file_pdf: Express.Multer.File,
    @Request() req,
  ) {
    let namaFile_pdf;

    try {
      if (file_pdf) {
        // Validate file size
        if (file_pdf.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = file_pdf.originalname.substring(
          file_pdf.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (file_pdf.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          file_pdf.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(file_pdf.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        'Error saat menyimpan file panduan pedoman',
      );
    }

    const data = await this.panduanPedomanService.create(
      createPanduanPedomanDto,
      namaFile_pdf,
      // req.headers.id_user,
    );
    return {
      success: true,
      message: 'Data Panduan Pedoman Berhasil Di Tambah',
    };
  }

  @Get()
  async findAll() {
    const data = await this.panduanPedomanService.findAll();
    return {
      data: data,
      success: true,
      message: 'Data Panduan Pedoman ',
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.panduanPedomanService.findOne(id);
    return {
      data: data,
      success: true,
      message: 'Data Panduan Pedoman By ID',
    };
  }

  @Post(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        judul: { type: 'string' },
        kategori: { type: 'string' },
        // isi: { type: 'text' },
        tanggal: { type: 'text' },
        file_pdf: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('file_pdf'))
  async update(
    @Param('id') id: string,
    @Body() updatePanduanPedomanDto: UpdatePanduanPedomanDto,
    @UploadedFile() file_pdf: Express.Multer.File,
    @Request() req,
  ) {
    let namaFile_pdf;

    try {
      if (file_pdf) {
        // Validate file size
        if (file_pdf.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File PDF terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = file_pdf.originalname.substring(
          file_pdf.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file tidak diizinkan');
        }

        // Validate MIME type for PDF
        if (file_pdf.mimetype !== 'application/pdf') {
          throw new BadRequestException('File harus berformat PDF');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile_pdf = StorageConfig.generateSecureFilename(
          file_pdf.originalname,
        );
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile_pdf);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(file_pdf.buffer);
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        'Error saat menyimpan file panduan pedoman',
      );
    }

    await this.panduanPedomanService.update(
      id,
      updatePanduanPedomanDto,
      namaFile_pdf,
    );
    return {
      success: true,
      message: 'Data Panduan Pedoman Berhasil Di Update',
    };
  }

  @Post('delete/:id')
  async remove(@Param('id') id: string) {
    const data = await this.panduanPedomanService.remove(id);
    return {
      success: true,
      message: 'Data Panduan Pedoman Berhasil Di Hapus',
    };
  }
}
