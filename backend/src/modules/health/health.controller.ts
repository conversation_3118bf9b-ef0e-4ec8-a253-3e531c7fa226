import { Controller, Get } from '@nestjs/common';
import {
  HealthCheck,
  HealthCheckService,
  HttpHealthIndicator,
  TypeOrmHealthIndicator,
} from '@nestjs/terminus';
import { Public } from 'src/common/decorators';

@Controller('health')
export class HealthController {
  constructor(
    private healthCheckService: HealthCheckService,
    private http: HttpHealthIndicator,
    private db: TypeOrmHealthIndicator,
  ) {}

  @Public()
  @Get('/live')
  @HealthCheck()
  checkHealth2() {
    let data = this.healthCheckService.check([
      () =>
        this.http.pingCheck(
          'Basic Check Swagger',
          'http://localhost:3000/api/v1',
        ),
    ]);
    return data;
  }

  @Public()
  @Get('/ready')
  @HealthCheck()
  checkHealth() {
    let data = this.healthCheckService.check([
      () => this.db.pingCheck('library'),
      () => this.db.pingCheck('database'),
    ]);
    return data;
  }
}
