import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { useMutation, useQuery } from '@tanstack/vue-query';
import { tokenStorage } from '@/lib/secure-storage';
import type {
  Operator,
  OperatorLoginRequest,
  OperatorLoginResponse,
  ChatRoom,
  ChatMessage,
  TypingIndicator
} from '../types/operator.types';

export const useOperatorStore = defineStore('operator', () => {
  // State
  const operator = ref<Operator | null>(null);
  const accessToken = ref<string | null>(null);
  const refreshToken = ref<string | null>(null);
  const isAuthenticated = ref<boolean>(false);
  const isLoading = ref<boolean>(false);
  const error = ref<string | null>(null);

  // Chat state
  const chatRooms = ref<ChatRoom[]>([]);
  const selectedRoomId = ref<string | null>(null);
  const messages = ref<Record<string, ChatMessage[]>>({});
  const typingIndicators = ref<Record<string, TypingIndicator[]>>({});
  const isConnected = ref<boolean>(false);

  // Computed
  const selectedRoom = computed(() => 
    chatRooms.value.find(room => room.id === selectedRoomId.value)
  );

  const selectedRoomMessages = computed(() => 
    selectedRoomId.value ? messages.value[selectedRoomId.value] || [] : []
  );

  const totalUnreadCount = computed(() =>
    chatRooms.value.reduce((total, room) => total + room.unreadCount, 0)
  );

  // API Functions
  const loginApi = async (credentials: OperatorLoginRequest): Promise<OperatorLoginResponse> => {
    const response = await fetch('/api/chat/operator/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Login failed');
    }

    return response.json();
  };

  const fetchChatRoomsApi = async (): Promise<ChatRoom[]> => {
    if (!accessToken.value) throw new Error('No access token');

    const response = await fetch('/api/chat/operator/chat-rooms', {
      headers: {
        'Authorization': `Bearer ${accessToken.value}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch chat rooms');
    }

    const result = await response.json();
    return result.data || [];
  };

  // TanStack Query Mutations and Queries

  const loginMutation = useMutation({
    mutationFn: loginApi,
    onSuccess: (data: OperatorLoginResponse) => {
      operator.value = data.operator;
      accessToken.value = data.accessToken;
      refreshToken.value = data.refreshToken;
      isAuthenticated.value = true;

      // Store tokens securely using encrypted storage
      tokenStorage.setAccessToken(data.accessToken);
      tokenStorage.setRefreshToken(data.refreshToken);
      tokenStorage.setOperatorData(data.operator);

      error.value = null;
    },
    onError: (err: Error) => {
      error.value = err.message;
    },
  });

  const chatRoomsQuery = useQuery({
    queryKey: ['chatRooms'],
    queryFn: fetchChatRoomsApi,
    enabled: computed(() => !!accessToken.value),
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Watch for query data changes
  watch(() => chatRoomsQuery.data.value, (data) => {
    if (data) {
      chatRooms.value = data;
    }
  });

  watch(() => chatRoomsQuery.error.value, (err) => {
    if (err) {
      error.value = err.message;
    }
  });

  // Actions
  const login = async (credentials: OperatorLoginRequest): Promise<void> => {
    await loginMutation.mutateAsync(credentials);
  };

  const logout = (): void => {
    operator.value = null;
    accessToken.value = null;
    refreshToken.value = null;
    isAuthenticated.value = false;
    chatRooms.value = [];
    selectedRoomId.value = null;
    messages.value = {};
    typingIndicators.value = {};
    isConnected.value = false;

    // Clear secure storage
    tokenStorage.clearTokens();
  };

  const initializeFromStorage = (): void => {
    try {
      const token = tokenStorage.getAccessToken();
      const refreshTokenStored = tokenStorage.getRefreshToken();
      const operatorData = tokenStorage.getOperatorData();

      if (token && refreshTokenStored && operatorData) {
        accessToken.value = token;
        refreshToken.value = refreshTokenStored;
        operator.value = operatorData;
        isAuthenticated.value = true;
      }
    } catch (err) {
      console.error('Failed to retrieve stored operator data:', err);
      logout();
    }
  };

  const fetchChatRooms = async (): Promise<void> => {
    if (!accessToken.value) return;

    try {
      const response = await fetch('/api/chat/operator/chat-rooms', {
        headers: {
          'Authorization': `Bearer ${accessToken.value}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch chat rooms');
      }

      const result = await response.json();
      chatRooms.value = result.data || [];
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch chat rooms';
    }
  };

  const fetchRoomMessages = async (roomId: string): Promise<void> => {
    if (!accessToken.value) return;

    try {
      const response = await fetch(`/api/chat/rooms/${roomId}/messages`, {
        headers: {
          'Authorization': `Bearer ${accessToken.value}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch messages');
      }

      const result = await response.json();
      messages.value[roomId] = result.data?.messages || [];
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch messages';
    }
  };

  const selectRoom = async (roomId: string): Promise<void> => {
    selectedRoomId.value = roomId;
    
    // Fetch messages if not already loaded
    if (!messages.value[roomId]) {
      await fetchRoomMessages(roomId);
    }
  };

  const addMessage = (message: ChatMessage): void => {
    if (!messages.value[message.roomId]) {
      messages.value[message.roomId] = [];
    }
    messages.value[message.roomId].push(message);

    // Update last message in room
    const room = chatRooms.value.find(r => r.id === message.roomId);
    if (room) {
      room.lastMessage = {
        id: message.id,
        content: message.content,
        timestamp: message.timestamp,
        senderType: message.senderType,
        senderName: message.senderName,
      };
      
      // Increment unread count if message is from user and room is not selected
      if (message.senderType === 'user' && selectedRoomId.value !== message.roomId) {
        room.unreadCount++;
      }
    }
  };

  const updateRoom = (updatedRoom: ChatRoom): void => {
    const index = chatRooms.value.findIndex(room => room.id === updatedRoom.id);
    if (index !== -1) {
      chatRooms.value[index] = updatedRoom;
    } else {
      chatRooms.value.unshift(updatedRoom);
    }
  };

  const addNewRoom = (room: ChatRoom): void => {
    const exists = chatRooms.value.some(r => r.id === room.id);
    if (!exists) {
      chatRooms.value.unshift(room);
    }
  };

  const updateTypingIndicator = (indicator: TypingIndicator): void => {
    if (!typingIndicators.value[indicator.roomId]) {
      typingIndicators.value[indicator.roomId] = [];
    }

    const indicators = typingIndicators.value[indicator.roomId];
    const existingIndex = indicators.findIndex(
      i => i.userId === indicator.userId && i.operatorId === indicator.operatorId
    );

    if (indicator.isTyping) {
      if (existingIndex === -1) {
        indicators.push(indicator);
      } else {
        indicators[existingIndex] = indicator;
      }
    } else {
      if (existingIndex !== -1) {
        indicators.splice(existingIndex, 1);
      }
    }
  };

  const markRoomAsRead = (roomId: string): void => {
    const room = chatRooms.value.find(r => r.id === roomId);
    if (room) {
      room.unreadCount = 0;
    }

    // Mark all messages in room as read
    if (messages.value[roomId]) {
      messages.value[roomId].forEach(message => {
        if (message.senderType === 'user') {
          message.isRead = true;
        }
      });
    }
  };

  const clearError = (): void => {
    error.value = null;
  };

  const setConnectionStatus = (connected: boolean): void => {
    isConnected.value = connected;
  };

  return {
    // State
    operator,
    accessToken,
    refreshToken,
    isAuthenticated,
    isLoading,
    error,
    chatRooms,
    selectedRoomId,
    messages,
    typingIndicators,
    isConnected,

    // Computed
    selectedRoom,
    selectedRoomMessages,
    totalUnreadCount,

    // Actions
    login,
    logout,
    initializeFromStorage,
    fetchChatRooms,
    fetchRoomMessages,
    selectRoom,
    addMessage,
    updateRoom,
    addNewRoom,
    updateTypingIndicator,
    markRoomAsRead,
    clearError,
    setConnectionStatus,
  };
});