import { computed, type Ref } from 'vue';
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query';
import type { 
  OperatorLoginRequest, 
  OperatorLoginResponse,
  ChatRoom,
  ChatMessage
} from '../types/operator.types';

// API Functions
const loginApi = async (credentials: OperatorLoginRequest): Promise<OperatorLoginResponse> => {
  const response = await fetch('/api/chat/operator/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(credentials),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Login failed');
  }

  return response.json();
};

const fetchChatRoomsApi = async (token: string): Promise<ChatRoom[]> => {
  const response = await fetch('/api/chat/operator/chat-rooms', {
    headers: {
      'Authorization': `Bear<PERSON> ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch chat rooms');
  }

  const result = await response.json();
  return result.data || [];
};

const fetchRoomMessagesApi = async (roomId: string, token: string): Promise<ChatMessage[]> => {
  const response = await fetch(`/api/chat/rooms/${roomId}/messages`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch messages');
  }

  const result = await response.json();
  return result.data?.messages || [];
};

export function useOperatorApi(accessToken: Ref<string | null> | string | null) {
  const queryClient = useQueryClient();
  
  // Convert to ref if it's a raw value
  const tokenRef = typeof accessToken === 'string' || accessToken === null
    ? computed(() => accessToken)
    : accessToken;

  // Login Mutation
  const loginMutation = useMutation({
    mutationFn: loginApi,
  });

  // Chat Rooms Query
  const chatRoomsQuery = useQuery({
    queryKey: ['chatRooms'],
    queryFn: () => fetchChatRoomsApi(tokenRef.value!),
    enabled: computed(() => !!tokenRef.value),
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Room Messages Query Factory
  const useRoomMessagesQuery = (roomId: Ref<string | null> | string | null) => {
    const roomIdRef = typeof roomId === 'string' || roomId === null
      ? computed(() => roomId)
      : roomId;
      
    return useQuery({
      queryKey: ['roomMessages', roomIdRef],
      queryFn: () => fetchRoomMessagesApi(roomIdRef.value!, tokenRef.value!),
      enabled: computed(() => !!roomIdRef.value && !!tokenRef.value),
    });
  };

  // Refresh functions
  const refreshChatRooms = () => {
    queryClient.invalidateQueries({ queryKey: ['chatRooms'] });
  };

  const refreshRoomMessages = (roomId: string) => {
    queryClient.invalidateQueries({ queryKey: ['roomMessages', roomId] });
  };

  return {
    // Mutations
    loginMutation,
    
    // Queries
    chatRoomsQuery,
    useRoomMessagesQuery,
    
    // Refresh functions
    refreshChatRooms,
    refreshRoomMessages,
    
    // Query client
    queryClient,
  };
}