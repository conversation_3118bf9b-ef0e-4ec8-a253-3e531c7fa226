# Frontend Development Environment Variables
# Copy this file to .env.development in the frontend directory

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Base API URL (relative path for same-domain deployment)
VITE_API_BASE_URL=/api

# Backend URL (localhost for development)
VITE_BACKEND_URL=http://localhost

# WebSocket URL for real-time features
VITE_WS_URL=ws://localhost

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Application title
VITE_APP_TITLE=CSRIT Bank Sulsel (Dev)

# Application version
VITE_APP_VERSION=1.0.0-dev

# Environment name
VITE_APP_ENV=development

# =============================================================================
# FEATURE FLAGS (All enabled for development)
# =============================================================================
VITE_FEATURE_CHAT=true
VITE_FEATURE_FILE_UPLOAD=true
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_DARK_MODE=true
VITE_FEATURE_ANALYTICS=false

# =============================================================================
# EXTERNAL SERVICES (Development/Test)
# =============================================================================
# Google Analytics (disabled for development)
VITE_GA_TRACKING_ID=

# Sentry for error tracking (disabled for development)
VITE_SENTRY_DSN=

# =============================================================================
# UI CONFIGURATION
# =============================================================================
# Default language
VITE_DEFAULT_LOCALE=id

# Available languages
VITE_AVAILABLE_LOCALES=id,en

# Theme configuration
VITE_DEFAULT_THEME=light

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Content Security Policy (relaxed for development)
VITE_CSP_NONCE=

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# API timeout in milliseconds (longer for development)
VITE_API_TIMEOUT=60000

# Request retry attempts
VITE_API_RETRY_ATTEMPTS=1

# Cache duration for static assets (shorter for development)
VITE_CACHE_DURATION=0

# =============================================================================
# DEBUGGING (Enabled for development)
# =============================================================================
# Debug mode
VITE_DEBUG=true

# Show API logs
VITE_LOG_API_CALLS=true

# Show performance metrics
VITE_SHOW_PERFORMANCE=true

# Hot module replacement
VITE_HMR=true

# Source maps
VITE_SOURCE_MAPS=true
