# Stage 1: Build the application
FROM node:18-alpine AS build-stage

# Set working directory
WORKDIR /app

# Copy package.json and pnpm-lock.yaml (or appropriate lock file)
# We copy these first to leverage Docker cache for dependencies
COPY package.json pnpm-lock.yaml ./
# If you are not using pnpm-lock.yaml, adjust to your lock file e.g. package-lock.json

# Install pnpm globally if not available in the base image, or use npx pnpm
# Assuming pnpm is the package manager for the frontend
RUN npm install -g pnpm

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy the rest of the application code
COPY . .

# Build the application
# The build script should be defined in frontend/package.json
# e.g., "build": "vite build"
RUN pnpm run build

# Stage 2: Production environment
FROM nginx:alpine AS production-stage

# Copy built assets from the build stage
# Vite typically builds to a 'dist' folder in the project root
COPY --from=build-stage /app/dist /usr/share/nginx/html

# Expose port 80 for Nginx
EXPOSE 80

# Command to run Nginx
CMD ["nginx", "-g", "daemon off;"]