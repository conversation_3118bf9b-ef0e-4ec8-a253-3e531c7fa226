# Docker Compose configuration for development with Nginx reverse proxy
# For local development: frontend at http://localhost, API at http://localhost/api

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: csrit-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-csrit_backend_dev}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASS:-password}
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"  # Different port for dev to avoid conflicts
    networks:
      - csrit-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-csrit_backend_dev}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NestJS Backend (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: csrit-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-csrit_backend_dev}
      DB_USER: ${DB_USER:-postgres}
      DB_PASS: ${DB_PASS:-password}
      JWT_ACCESS_TOKEN_SECRET: ${JWT_ACCESS_TOKEN_SECRET:-dev_access_secret}
      JWT_REFRESH_TOKEN_SECRET: ${JWT_REFRESH_TOKEN_SECRET:-dev_refresh_secret}
      JWT_ACCESS_TOKEN_EXPIRATION_TIME: ${JWT_ACCESS_TOKEN_EXPIRATION_TIME:-3600}
      JWT_REFRESH_TOKEN_EXPIRATION_TIME: ${JWT_REFRESH_TOKEN_EXPIRATION_TIME:-86400}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost}
      PORT: 3000
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_dev_uploads:/app/uploads
      - backend_dev_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - csrit-dev-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    expose:
      - "3000"
    command: npm run start:dev

  # Vue.js Frontend (Development build)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
      args:
        VITE_API_BASE_URL: /api
        VITE_BACKEND_URL: http://localhost
        VITE_WS_URL: ws://localhost
    container_name: csrit-frontend-dev
    volumes:
      - frontend_dev_dist:/app/dist
    networks:
      - csrit-dev-network
    command: npm run build

  # Nginx Reverse Proxy (Development)
  nginx:
    image: nginx:alpine
    container_name: csrit-nginx-dev
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      # Development nginx configuration
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      # Frontend static files
      - frontend_dev_dist:/usr/share/nginx/html:ro
      # Logs
      - nginx_dev_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - csrit-dev-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (Development)
  redis:
    image: redis:7-alpine
    container_name: csrit-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"  # Different port for dev
    networks:
      - csrit-dev-network
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_dev_data:
    driver: local
  backend_dev_uploads:
    driver: local
  backend_dev_logs:
    driver: local
  frontend_dev_dist:
    driver: local
  nginx_dev_logs:
    driver: local
  redis_dev_data:
    driver: local

networks:
  csrit-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
