# CSRIT Bank Sulsel - Production Deployment Guide

This guide provides comprehensive instructions for deploying the CSRIT Bank Sulsel application with same-domain configuration (frontend at `https://foobar.com`, API at `https://foobar.com/api`).

## 🏗️ Architecture Overview

The application uses a reverse proxy architecture with Nginx:
- **Frontend**: Vue.js 3 application served as static files
- **Backend**: NestJS API server with global `/api` prefix
- **Reverse Proxy**: Nginx routes requests to appropriate services
- **Database**: PostgreSQL with Redis for caching
- **SSL**: HTTPS termination at Nginx level

## 📋 Prerequisites

- Docker and Docker Compose installed
- Domain name configured (foobar.com)
- SSL certificates (for production)
- Minimum 2GB RAM, 20GB disk space

## 🚀 Quick Start

### Development Deployment

```bash
# Clone the repository
git clone <repository-url>
cd be-wbs-kg

# Deploy development environment
./scripts/deploy.sh dev
```

The application will be available at:
- Frontend: http://localhost
- API: http://localhost/api
- Health Check: http://localhost/health

### Production Deployment

```bash
# Deploy production environment
./scripts/deploy.sh prod
```

The application will be available at:
- Frontend: https://foobar.com
- API: https://foobar.com/api
- Health Check: https://foobar.com/health

## 📁 Project Structure

```
be-wbs-kg/
├── backend/                 # NestJS backend application
├── frontend/               # Vue.js frontend application
├── nginx/                  # Nginx configurations
│   ├── nginx.conf         # Production configuration
│   └── nginx.dev.conf     # Development configuration
├── scripts/               # Deployment scripts
│   ├── deploy.sh         # Main deployment script
│   └── generate-ssl-certs.sh # SSL certificate generation
├── ssl/                   # SSL certificates directory
│   ├── certs/            # Certificate files
│   └── private/          # Private key files
├── docker-compose.nginx.yml # Production Docker Compose
├── docker-compose.dev.yml   # Development Docker Compose
└── DEPLOYMENT.md          # This file
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env.prod)
```env
NODE_ENV=production
DB_HOST=postgres
DB_PORT=5432
DB_NAME=csrit_backend
DB_USER=postgres
DB_PASS=your_secure_password
JWT_ACCESS_TOKEN_SECRET=your_jwt_secret
JWT_REFRESH_TOKEN_SECRET=your_refresh_secret
CORS_ORIGIN=https://foobar.com
REDIS_PASSWORD=your_redis_password
```

#### Frontend (Built-in)
- `VITE_API_BASE_URL=/api`
- `VITE_BACKEND_URL=https://foobar.com`
- `VITE_WS_URL=wss://foobar.com`

### SSL Certificates

#### Development (Self-signed)
```bash
./scripts/generate-ssl-certs.sh dev
```

#### Production Options

**Option 1: Let's Encrypt (Recommended)**
```bash
# Install Certbot
sudo apt-get install certbot python3-certbot-nginx

# Generate certificate
sudo certbot --nginx -d foobar.com -d www.foobar.com

# Copy to project
sudo cp /etc/letsencrypt/live/foobar.com/fullchain.pem ssl/certs/foobar.com.crt
sudo cp /etc/letsencrypt/live/foobar.com/privkey.pem ssl/private/foobar.com.key
```

**Option 2: Commercial Certificate**
```bash
# Generate CSR
openssl req -new -newkey rsa:2048 -nodes \
  -keyout ssl/private/foobar.com.key \
  -out foobar.com.csr

# Submit CSR to CA and place certificate at ssl/certs/foobar.com.crt
```

## 🐳 Docker Commands

### Development
```bash
# Start development environment
./scripts/deploy.sh dev

# Stop services
./scripts/deploy.sh stop dev

# View logs
./scripts/deploy.sh logs dev

# View specific service logs
./scripts/deploy.sh logs dev backend

# Check health
./scripts/deploy.sh health dev
```

### Production
```bash
# Start production environment
./scripts/deploy.sh prod

# Stop services
./scripts/deploy.sh stop prod

# View logs
./scripts/deploy.sh logs prod

# Check health
./scripts/deploy.sh health prod
```

### Manual Docker Commands
```bash
# Development
docker-compose -f docker-compose.dev.yml --env-file .env.dev up -d
docker-compose -f docker-compose.dev.yml down

# Production
docker-compose -f docker-compose.nginx.yml --env-file .env.prod up -d
docker-compose -f docker-compose.nginx.yml down
```

## 🔍 Monitoring and Troubleshooting

### Health Checks
- Backend: `GET /health`
- Frontend: `GET /` (should return index.html)
- Database: Automatic health checks in Docker Compose

### Log Locations
- Nginx: `docker-compose logs nginx`
- Backend: `docker-compose logs backend`
- Database: `docker-compose logs postgres`

### Common Issues

#### 1. SSL Certificate Errors
```bash
# Check certificate validity
openssl x509 -in ssl/certs/foobar.com.crt -text -noout

# Regenerate development certificate
./scripts/generate-ssl-certs.sh dev
```

#### 2. API Not Accessible
```bash
# Check backend health
curl http://localhost:3000/health  # Direct backend
curl http://localhost/api/health   # Through nginx

# Check nginx configuration
docker exec csrit-nginx nginx -t
```

#### 3. Database Connection Issues
```bash
# Check database logs
docker-compose logs postgres

# Test database connection
docker exec -it csrit-postgres psql -U postgres -d csrit_backend
```

## 🔒 Security Considerations

### Production Security Checklist
- [ ] Valid SSL certificates installed
- [ ] Strong database passwords
- [ ] JWT secrets are cryptographically secure
- [ ] CORS origins properly configured
- [ ] Rate limiting enabled in Nginx
- [ ] Security headers configured
- [ ] Non-root user in containers
- [ ] Firewall configured (ports 80, 443 only)

### Security Headers (Configured in Nginx)
- `X-Frame-Options: SAMEORIGIN`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000`
- `Content-Security-Policy: ...`

## 📊 Performance Optimization

### Nginx Optimizations
- Gzip compression enabled
- Static file caching (1 year for assets)
- Connection keep-alive
- Buffer optimization

### Application Optimizations
- Production builds minified
- Tree-shaking enabled
- Code splitting implemented
- Health checks configured

## 🔄 Updates and Maintenance

### Application Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and redeploy
./scripts/deploy.sh prod
```

### SSL Certificate Renewal (Let's Encrypt)
```bash
# Automatic renewal (add to crontab)
0 12 * * * /usr/bin/certbot renew --quiet

# Manual renewal
sudo certbot renew
```

### Database Backups
```bash
# Create backup
docker exec csrit-postgres pg_dump -U postgres csrit_backend > backup.sql

# Restore backup
docker exec -i csrit-postgres psql -U postgres csrit_backend < backup.sql
```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review application logs
3. Verify configuration files
4. Contact the development team

## 🔗 Related Documentation

- [NestJS Documentation](https://docs.nestjs.com/)
- [Vue.js Documentation](https://vuejs.org/)
- [Nginx Documentation](https://nginx.org/en/docs/)
- [Docker Documentation](https://docs.docker.com/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)
