# Project Title (be-wbs-kg Monorepo)

This project is a monorepo integrating a Vue3 frontend with a NestJS backend, managed using pnpm workspaces.

## Prerequisites

*   Node.js: >=18
*   pnpm: >=8

## Quickstart

1.  **Install Dependencies:**
    Install all dependencies for both frontend and backend workspaces.
    ```bash
    pnpm install
    ```

2.  **Run Development Servers:**
    Start both frontend (Vue3 on port 5173) and backend (NestJS on port 3000) concurrently.
    ```bash
    pnpm run dev
    ```
    *   Frontend will be accessible at `http://localhost:5173`
    *   Backend will be accessible at `http://localhost:3000`
    *   API requests from the frontend starting with `/api` will be proxied to the backend.

## Project Structure

```
/
├── backend/          # NestJS Backend Application
│   ├── src/
│   ├── Dockerfile
│   └── package.json
├── frontend/         # Vue3 + Vite + Tailwind CSS + TanStack Query + Pinia Frontend
│   ├── src/
│   ├── Dockerfile
│   └── package.json
├── .gitlab-ci.yml    # GitLab CI/CD Pipeline Configuration
├── package.json      # Root workspace with pnpm scripts
├── pnpm-workspace.yaml # pnpm workspace configuration
└── README.md         # This file
```

## Development Scripts (Root `package.json`)

*   `pnpm run dev:frontend`: Starts only the frontend development server (`vite --port 5173`).
*   `pnpm run dev:backend`: Starts only the backend development server (`nest start --watch --port 3000`).
*   `pnpm run dev`: Starts both frontend and backend concurrently.

## DevOps Guide

### Docker Builds

Docker images can be built for each service individually. Ensure you are in the respective service directory or adjust paths in the build command.

*   **Build Frontend Docker Image:**
    ```bash
    # Navigate to the frontend directory
    cd frontend
    docker build -t your-repo/frontend:latest .
    # Or from the root
    # docker build -t your-repo/frontend:latest -f frontend/Dockerfile ./frontend
    ```

*   **Build Backend Docker Image:**
    ```bash
    # Navigate to the backend directory
    cd backend
    docker build -t your-repo/backend:latest .
    # Or from the root
    # docker build -t your-repo/backend:latest -f backend/Dockerfile ./backend
    ```
    *(Replace `your-repo` with your actual Docker repository/namespace.)*

### Environment Variables for Production

*   **`VITE_API_BASE_URL` (Frontend):**
    This environment variable is crucial for the frontend to know where the production backend API is located.
    *   **During CI/CD:** This is typically injected during the deployment phase. The `.gitlab-ci.yml` is set up to use variables like `VITE_API_BASE_URL_PRODUCTION` and `VITE_API_BASE_URL_DEVELOPMENT` defined in GitLab CI/CD settings.
    *   **Runtime Configuration:** For flexibility, the frontend can be configured to fetch this URL from a `/config.js` file at runtime. This file would be provisioned by the deployment process.

*   **Backend Environment Variables:**
    The NestJS backend ([`backend/`](backend/)) likely requires its own set of environment variables (e.g., database connection strings, API keys, JWT secrets). These should be managed according to standard NestJS practices (e.g., using a `.env` file in production, configured via the deployment environment). Refer to [`backend/.env.example`](backend/.env.example) for typical variables.

### Cloud Deployment Steps (Conceptual)

Deployment is handled by the `.gitlab-ci.yml` pipeline:

1.  **Build:** On commit/merge to relevant branches, Docker images for frontend and backend are built and pushed to the container registry (`$CONTAINER_REGISTRY`).
2.  **Deploy (Dev/Prod):**
    *   The pipeline triggers deployment jobs for different environments (e.g., `development`, `production`).
    *   These jobs pull the respective Docker images.
    *   Actual deployment commands (e.g., `kubectl apply`, `helm upgrade`, custom scripts via `ssh`) are executed to update the services in the target cloud environment.
    *   The `VITE_API_BASE_URL` is made available to the frontend deployment as needed.

## FAQ

*   **Resolving Port Conflicts (5173/3000):**
    *   The frontend runs on port `5173` and the backend on `3000` by default, as configured in [`frontend/vite.config.ts`](frontend/vite.config.ts) and the backend's startup script (e.g., `nest start --port 3000`).
    *   If these ports are in use, you can:
        *   Stop the conflicting application.
        *   Modify the port in [`frontend/vite.config.ts`](frontend/vite.config.ts) for the frontend.
        *   Modify the port in the `dev:backend` script in the root [`package.json`](package.json) (and potentially in [`backend/src/main.ts`](backend/src/main.ts) for production builds if hardcoded there).

*   **CORS Error Mitigation:**
    *   During local development, the Vite proxy configured in [`frontend/vite.config.ts`](frontend/vite.config.ts) handles requests from `/api` on the frontend to the backend on `http://localhost:3000`. This should prevent most CORS issues locally.
    *   For deployed environments, ensure your NestJS backend has CORS configured appropriately to allow requests from your frontend's domain. See NestJS documentation on `enableCors()`.

*   **Dependency Mismatch Troubleshooting:**
    *   This project uses pnpm workspaces. Dependencies for the frontend are in [`frontend/package.json`](frontend/package.json) and for the backend in [`backend/package.json`](backend/package.json). The root [`package.json`](package.json) is primarily for workspace orchestration and root-level dev tools like `concurrently`.
    *   Always run `pnpm install` from the **project root** to ensure dependencies across workspaces are correctly installed and linked.
    *   If you encounter issues, try removing `node_modules` directories from the root, `frontend`, and `backend`, as well as `pnpm-lock.yaml` from the root, and then run `pnpm install` again from the root.
