# CSRIT Bank Sulsel - Web Application 🏦

> **For Junior Developers**: This guide is written specifically for frontend, backend, and DevOps junior developers. We'll explain everything step by step!

A modern full-stack web application for CSRIT Bank Sulsel built with **NestJS** (backend) and **Vue.js 3** (frontend). This project uses a **same-domain deployment architecture** where both frontend and backend are served from the same domain to avoid CORS issues.

## 🌟 What Makes This Project Special

- **Same-Domain Architecture**: Frontend at `https://foobar.com`, API at `https://foobar.com/api` (no CORS issues!)
- **Modern Tech Stack**: NestJS + Vue.js 3 + TypeScript + PostgreSQL + Redis
- **Real-time Features**: WebSocket-based chat system
- **Production Ready**: Docker containerization with nginx reverse proxy
- **Security First**: JWT authentication, OWASP compliance, rate limiting
- **Developer Friendly**: Hot reload, comprehensive documentation, automated deployment

## 📋 Table of Contents

- [Quick Start (5 minutes)](#-quick-start-5-minutes)
- [Understanding the Architecture](#-understanding-the-architecture)
- [Development Setup](#-development-setup)
- [Code Style Guidelines](#-code-style-guidelines)
- [Customization Guide](#-customization-guide)
- [Same-Domain Explanation](#-same-domain-explanation)
- [API Documentation](#-api-documentation)
- [Deployment Guide](#-deployment-guide)
- [Troubleshooting](#-troubleshooting)

## 🚀 Quick Start (5 minutes)

### Step 1: Check Prerequisites

Make sure you have these installed:

```bash
# Check versions (run these commands in your terminal)
node --version    # Should show v18+ or v20+ (LTS recommended)
npm --version     # Should show 8+
docker --version  # Should show 20+
git --version     # Should show 2+
```

**Don't have them?** Install:
- [Node.js LTS](https://nodejs.org/) (this includes npm)
- [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- [Git](https://git-scm.com/)

### Step 2: Get the Code

```bash
# Clone the repository
git clone <your-repository-url>
cd be-wbs-kg

# Make scripts executable (Mac/Linux)
chmod +x scripts/*.sh
```

### Step 3: Start Development Environment

```bash
# Setup environment files (creates .env files)
./scripts/setup-env.sh dev

# Start the entire application with Docker
./scripts/deploy.sh dev
```

**Wait 2-3 minutes** for everything to start up, then open:

- **🌐 Frontend**: http://localhost
- **🔧 API**: http://localhost/api
- **❤️ Health Check**: http://localhost/health

### Step 4: Stop When Done

```bash
./scripts/deploy.sh stop dev
```

**That's it!** You now have the full application running locally.

## 🏗️ Understanding the Architecture

### What is Same-Domain Architecture?

**Traditional Setup (has problems):**
```
Frontend: https://myapp.com      ← User visits this
Backend:  https://api.myapp.com  ← Frontend makes API calls here
```
❌ **Problem**: Browser blocks requests due to CORS (Cross-Origin Resource Sharing)

**Our Setup (no problems):**
```
Frontend: https://foobar.com     ← User visits this
Backend:  https://foobar.com/api ← Frontend makes API calls here
```
✅ **Solution**: Same domain = no CORS issues!

### How Does It Work?

We use **nginx as a reverse proxy**:

```
User Request → nginx → Routes to correct service
├── https://foobar.com/      → Frontend (Vue.js files)
├── https://foobar.com/api/  → Backend (NestJS API)
└── https://foobar.com/health → Health check
```

### Project Structure

```
be-wbs-kg/
├── 📁 backend/                    # NestJS Backend (API server)
│   ├── 📁 src/
│   │   ├── 📁 modules/           # Feature modules (auth, users, etc.)
│   │   ├── 📁 common/            # Shared code (guards, decorators)
│   │   └── 📄 main.ts            # App entry point (has global /api prefix)
│   ├── 📄 Dockerfile             # How to build backend container
│   └── 📄 package.json           # Backend dependencies
│
├── 📁 frontend/                   # Vue.js 3 Frontend (user interface)
│   ├── 📁 src/
│   │   ├── 📁 components/        # Reusable UI components
│   │   ├── 📁 views/             # Page components
│   │   ├── 📁 stores/            # Pinia state management
│   │   ├── 📁 services/          # API communication
│   │   └── 📁 config/            # Configuration files
│   ├── 📄 Dockerfile             # How to build frontend container
│   └── 📄 package.json           # Frontend dependencies
│
├── 📁 nginx/                      # Reverse proxy configuration
│   ├── 📄 nginx.conf             # Production config (with SSL)
│   └── 📄 nginx.dev.conf         # Development config (no SSL)
│
├── 📁 scripts/                    # Automation scripts
│   ├── 📄 deploy.sh              # Main deployment script
│   ├── 📄 setup-env.sh           # Environment setup
│   └── 📄 generate-ssl-certs.sh  # SSL certificate generation
│
├── 📁 ssl/                        # SSL certificates (for HTTPS)
│   ├── 📁 certs/                 # Certificate files
│   └── 📁 private/               # Private key files
│
├── 📄 docker-compose.nginx.yml    # Production deployment config
├── 📄 docker-compose.dev.yml      # Development deployment config
├── 📄 .env.prod.example           # Production environment template
├── 📄 .env.dev.example            # Development environment template
└── 📄 DEPLOYMENT.md               # Detailed deployment guide
```

### 🔧 Technology Stack Explained

#### **Why Redis? (Real-time Focus)**

Redis is used specifically for **real-time features**, NOT for caching:

- **💬 Chat System**: Real-time message queuing and WebSocket session management
- **🔔 Live Notifications**: Pub/Sub for instant notifications across multiple browser tabs
- **👥 Online User Tracking**: Track which users are currently active in chat
- **🔄 WebSocket State**: Manage WebSocket connections across server restarts

**Important**: We deliberately **avoid application caching** to ensure real-time data updates from the database.

#### **Authentication Strategy**

- **JWT Tokens**: Stateless authentication (no server-side sessions)
- **No Redis Sessions**: JWT handles all session management
- **Captcha**: Temporary in-memory storage only (not for persistent sessions)

This approach ensures **immediate data consistency** for banking operations while using Redis only for features that require real-time communication.

## 💻 Development Setup

### Option 1: Docker Development (Recommended for Beginners)

This is the **easiest way** to get started. Everything runs in containers, so you don't need to install databases or configure anything complex.

```bash
# Setup and start everything
./scripts/setup-env.sh dev
./scripts/deploy.sh dev

# View logs (helpful for debugging)
./scripts/deploy.sh logs dev

# Stop everything
./scripts/deploy.sh stop dev
```

**What this gives you:**
- ✅ Frontend at http://localhost
- ✅ Backend API at http://localhost/api
- ✅ PostgreSQL database (automatically configured)
- ✅ Redis for real-time features (automatically configured)
- ✅ nginx reverse proxy (handles routing)

### Option 2: Local Development (For Advanced Users)

If you want to run services locally (useful for debugging):

```bash
# Install dependencies
pnpm install

# Start database services only
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Start backend locally
cd backend
npm run start:dev

# Start frontend locally (in another terminal)
cd frontend
npm run dev
```

**Access:**
- Frontend: http://localhost:5173
- Backend: http://localhost:3000
- API through proxy: http://localhost:5173/api

### Development Commands

```bash
# 🐳 Docker Commands
./scripts/deploy.sh dev          # Start development environment
./scripts/deploy.sh stop dev     # Stop all services
./scripts/deploy.sh logs dev     # View all logs
./scripts/deploy.sh logs dev backend  # View specific service logs
./scripts/deploy.sh health dev   # Check service health

# 📦 Package Management (if using local development)
pnpm install                     # Install all dependencies
pnpm run dev                     # Start both frontend and backend
pnpm run dev:frontend           # Start only frontend
pnpm run dev:backend            # Start only backend

# 🔧 Utility Commands
./scripts/setup-env.sh status    # Check environment status
./scripts/setup-env.sh validate dev  # Validate development config
```

## 🎨 Code Style Guidelines

We follow industry best practices to keep our code clean and maintainable.

### Backend (NestJS) Style

```typescript
// ✅ Good: Use decorators and dependency injection
@Controller('v1/users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }
}

// ✅ Good: Use DTOs for validation
export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(8)
  password: string;
}

// ✅ Good: Use proper error handling
async createUser(createUserDto: CreateUserDto): Promise<User> {
  try {
    return await this.userRepository.save(createUserDto);
  } catch (error) {
    throw new BadRequestException('Failed to create user');
  }
}
```

### Frontend (Vue.js 3) Style

```vue
<!-- ✅ Good: Use Composition API with TypeScript -->
<template>
  <div class="user-list">
    <UserCard
      v-for="user in users"
      :key="user.id"
      :user="user"
      @edit="handleEdit"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useUsersStore } from '@/stores/users'
import type { User } from '@/types/user'

// ✅ Good: Use stores for state management
const usersStore = useUsersStore()
const users = ref<User[]>([])

// ✅ Good: Use composables for reusable logic
const { loading, error } = useApi()

onMounted(async () => {
  users.value = await usersStore.fetchUsers()
})

const handleEdit = (user: User) => {
  // Handle edit logic
}
</script>
```

### File Naming Conventions

```
📁 backend/src/
├── 📁 modules/
│   ├── 📁 auth/
│   │   ├── 📄 auth.controller.ts      # PascalCase + suffix
│   │   ├── 📄 auth.service.ts
│   │   ├── 📄 auth.module.ts
│   │   └── 📄 dto/
│   │       ├── 📄 login.dto.ts        # kebab-case + suffix
│   │       └── 📄 register.dto.ts
│   └── 📁 users/
│       ├── 📄 users.controller.ts
│       └── 📄 users.service.ts

📁 frontend/src/
├── 📁 components/
│   ├── 📄 UserCard.vue               # PascalCase for components
│   └── 📄 NavigationMenu.vue
├── 📁 views/
│   ├── 📄 HomePage.vue               # PascalCase for views
│   └── 📄 UserManagement.vue
├── 📁 stores/
│   ├── 📄 auth.ts                    # kebab-case for stores
│   └── 📄 users.ts
└── 📁 services/
    ├── 📄 api.service.ts             # kebab-case + suffix
    └── 📄 http.service.ts
```

## 🔧 Customization Guide

### Adding a New API Endpoint

**Step 1: Create the Backend Endpoint**

```typescript
// backend/src/modules/products/products.controller.ts
@Controller('v1/products')
export class ProductsController {
  @Get()
  async findAll(): Promise<Product[]> {
    return this.productsService.findAll();
  }

  @Post()
  async create(@Body() createProductDto: CreateProductDto): Promise<Product> {
    return this.productsService.create(createProductDto);
  }
}
```

**Step 2: Add Frontend API Call**

```typescript
// frontend/src/services/products.service.ts
import { httpService } from './http.service'
import type { Product, CreateProductDto } from '@/types/product'

export const productsService = {
  async getAll(): Promise<Product[]> {
    const response = await httpService.get('/v1/products')
    return response.data
  },

  async create(product: CreateProductDto): Promise<Product> {
    const response = await httpService.post('/v1/products', product)
    return response.data
  }
}
```

**Step 3: Use in Vue Component**

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { productsService } from '@/services/products.service'

const products = ref<Product[]>([])

onMounted(async () => {
  products.value = await productsService.getAll()
})
</script>
```

### Adding a New Page

**Step 1: Create the Vue Component**

```vue
<!-- frontend/src/views/ProductsPage.vue -->
<template>
  <div class="products-page">
    <h1>Products</h1>
    <ProductList :products="products" />
  </div>
</template>

<script setup lang="ts">
// Component logic here
</script>
```

**Step 2: Add Route**

```typescript
// frontend/src/router/index.ts
const routes = [
  // ... existing routes
  {
    path: '/products',
    name: 'Products',
    component: () => import('@/views/ProductsPage.vue')
  }
]
```

### Customizing Environment Variables

**Backend Environment Variables (.env.prod):**

```bash
# Database
DB_HOST=your-database-host
DB_NAME=your-database-name
DB_USER=your-database-user
DB_PASS=your-secure-password

# JWT Secrets (generate with: openssl rand -base64 32)
JWT_ACCESS_TOKEN_SECRET=your-jwt-secret
JWT_REFRESH_TOKEN_SECRET=your-refresh-secret

# External APIs
EXTERNAL_API_KEY=your-api-key
SMTP_HOST=your-email-host
```

**Frontend Environment Variables (.env.production):**

```bash
# API Configuration
VITE_API_BASE_URL=/api
VITE_BACKEND_URL=https://your-domain.com

# Feature Flags
VITE_FEATURE_CHAT=true
VITE_FEATURE_ANALYTICS=true

# External Services
VITE_GA_TRACKING_ID=your-google-analytics-id
```

## 🌐 Same-Domain Explanation (Deep Dive)

### Why Same-Domain Matters

**The CORS Problem:**
When your frontend (`https://myapp.com`) tries to call your API (`https://api.myapp.com`), the browser sees this as a "cross-origin request" and blocks it for security reasons.

**Traditional Solutions (and their problems):**
1. **Enable CORS on backend** - Works but has security implications
2. **Use a proxy during development** - Only works in development
3. **JSONP** - Old technique, limited functionality

**Our Solution: Same-Domain Architecture**
By serving both frontend and API from the same domain, we eliminate CORS entirely.

### How Our Backend Handles URLs

**The Magic: Global API Prefix**

In `backend/src/main.ts`, we add a global prefix:

```typescript
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 🎯 This line makes ALL routes start with /api
  app.setGlobalPrefix('api');

  await app.listen(3000);
}
```

**What this means:**

```typescript
// Before global prefix:
@Controller('v1/users')  // Creates route: /v1/users

// After global prefix:
@Controller('v1/users')  // Creates route: /api/v1/users
```

**All our controllers automatically get `/api` prefix:**

```typescript
@Controller('v1/auth')     // → /api/v1/auth
@Controller('v1/users')    // → /api/v1/users
@Controller('v1/documents') // → /api/v1/documents
@Controller('health')      // → /api/health
```

### How nginx Routes Requests

**nginx Configuration Logic:**

```nginx
server {
    listen 443 ssl;
    server_name foobar.com;

    # API requests → Backend
    location /api/ {
        proxy_pass http://backend:3000;
        # backend:3000 is our NestJS container
    }

    # Everything else → Frontend
    location / {
        try_files $uri $uri/ /index.html;
        # Serves Vue.js static files
    }
}
```

**Request Flow Examples:**

```
User visits: https://foobar.com/
nginx serves: frontend/dist/index.html

User visits: https://foobar.com/dashboard
nginx serves: frontend/dist/index.html (Vue router handles /dashboard)

Frontend calls: https://foobar.com/api/v1/users
nginx proxies to: http://backend:3000/api/v1/users
NestJS handles: /api/v1/users (because of global prefix)
```

### Frontend API Configuration

**How Frontend Knows Where to Call:**

```typescript
// frontend/src/config/api.config.ts
const config = {
  // In production: /api (same domain)
  // In development: /api (proxied by nginx)
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api',

  // Full backend URL for WebSockets, file downloads, etc.
  backendUrl: import.meta.env.VITE_BACKEND_URL || 'https://foobar.com'
}

// Usage in services:
export const authService = {
  async login(credentials: LoginDto) {
    // This calls: https://foobar.com/api/v1/auth/login
    return httpService.post('/v1/auth/login', credentials)
  }
}
```

## 📚 API Documentation

### Authentication Endpoints

```typescript
POST /api/v1/auth/login
Body: { email: string, password: string }
Response: { access_token: string, refresh_token: string, user: User }

POST /api/v1/auth/register
Body: { email: string, password: string, name: string }
Response: { access_token: string, refresh_token: string, user: User }

POST /api/v1/auth/refresh
Body: { refresh_token: string }
Response: { access_token: string }

POST /api/v1/auth/logout
Headers: { Authorization: "Bearer <token>" }
Response: { message: "Logged out successfully" }
```

### User Management Endpoints

```typescript
GET /api/v1/users
Headers: { Authorization: "Bearer <token>" }
Response: User[]

GET /api/v1/users/:id
Headers: { Authorization: "Bearer <token>" }
Response: User

PUT /api/v1/users/:id
Headers: { Authorization: "Bearer <token>" }
Body: { name?: string, email?: string }
Response: User

DELETE /api/v1/users/:id
Headers: { Authorization: "Bearer <token>" }
Response: { message: "User deleted" }
```

### File Upload Endpoints

```typescript
POST /api/v1/files/upload
Headers: { Authorization: "Bearer <token>" }
Body: FormData with file
Response: { id: string, filename: string, url: string }

GET /api/v1/files/:id
Headers: { Authorization: "Bearer <token>" }
Response: File download
```

### Health Check

```typescript
GET /api/health
Response: {
  status: "ok",
  database: "connected",
  redis: "connected",
  timestamp: "2024-01-01T00:00:00Z"
}
```

## 🚀 Deployment Guide

### Development Deployment

```bash
# Quick setup
./scripts/setup-env.sh dev
./scripts/deploy.sh dev

# Access at http://localhost
```

### Production Deployment

**Step 1: Prepare Environment**

```bash
# Setup production environment files
./scripts/setup-env.sh prod

# Edit .env.prod with your values
nano .env.prod
```

**Step 2: SSL Certificates**

```bash
# Option A: Let's Encrypt (recommended)
sudo certbot --nginx -d foobar.com
sudo cp /etc/letsencrypt/live/foobar.com/fullchain.pem ssl/certs/foobar.com.crt
sudo cp /etc/letsencrypt/live/foobar.com/privkey.pem ssl/private/foobar.com.key

# Option B: Self-signed (for testing)
./scripts/generate-ssl-certs.sh prod
```

**Step 3: Deploy**

```bash
# Deploy to production
./scripts/deploy.sh prod

# Check status
./scripts/deploy.sh health prod

# View logs
./scripts/deploy.sh logs prod
```

**Step 4: Verify**

- ✅ Frontend: https://foobar.com
- ✅ API: https://foobar.com/api/health
- ✅ SSL: Check certificate in browser

### Docker Commands Reference

```bash
# Development
./scripts/deploy.sh dev                    # Start development
./scripts/deploy.sh stop dev               # Stop development
./scripts/deploy.sh logs dev               # View all logs
./scripts/deploy.sh logs dev backend       # View backend logs only

# Production
./scripts/deploy.sh prod                   # Start production
./scripts/deploy.sh stop prod              # Stop production
./scripts/deploy.sh health prod            # Check health
./scripts/deploy.sh restart prod           # Restart services

# Manual Docker commands
docker-compose -f docker-compose.dev.yml up -d      # Start dev manually
docker-compose -f docker-compose.nginx.yml up -d    # Start prod manually
docker-compose logs -f backend                      # Follow backend logs
docker exec -it csrit-backend bash                  # Access backend container
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. "Port already in use" Error

**Problem**: Docker can't start because ports 80/443 are in use.

**Solution**:
```bash
# Check what's using the ports
sudo lsof -i :80
sudo lsof -i :443

# Stop conflicting services
sudo systemctl stop apache2  # If Apache is running
sudo systemctl stop nginx    # If nginx is running locally

# Or change ports in docker-compose.dev.yml
ports:
  - "8080:80"  # Use port 8080 instead
```

#### 2. "Cannot connect to database" Error

**Problem**: Backend can't connect to PostgreSQL.

**Solution**:
```bash
# Check if database container is running
docker ps | grep postgres

# Check database logs
./scripts/deploy.sh logs dev postgres

# Reset database
./scripts/deploy.sh stop dev
docker volume rm be-wbs-kg_postgres_data
./scripts/deploy.sh dev
```

#### 3. "API calls return 404" Error

**Problem**: Frontend can't reach backend API.

**Solution**:
```bash
# Check if backend is running
curl http://localhost/api/health

# Check nginx logs
./scripts/deploy.sh logs dev nginx

# Verify nginx configuration
docker exec csrit-nginx nginx -t
```

#### 4. "SSL Certificate Error" in Production

**Problem**: HTTPS not working properly.

**Solution**:
```bash
# Check certificate files exist
ls -la ssl/certs/foobar.com.crt
ls -la ssl/private/foobar.com.key

# Regenerate certificates
./scripts/generate-ssl-certs.sh prod

# Check nginx SSL configuration
docker exec csrit-nginx nginx -t
```

#### 5. "Frontend shows blank page"

**Problem**: Vue.js app not loading.

**Solution**:
```bash
# Check frontend build
./scripts/deploy.sh logs dev frontend

# Rebuild frontend
docker-compose -f docker-compose.dev.yml build frontend
./scripts/deploy.sh restart dev
```

#### 6. Environment Variables Not Working

**Problem**: App not reading environment variables.

**Solution**:
```bash
# Check environment files exist
./scripts/setup-env.sh status

# Validate configuration
./scripts/setup-env.sh validate dev

# Recreate environment files
rm .env.dev frontend/.env.development
./scripts/setup-env.sh dev
```

### Debug Commands

```bash
# Check all container status
docker ps -a

# Check container logs
docker logs csrit-backend
docker logs csrit-frontend
docker logs csrit-nginx
docker logs csrit-postgres

# Access container shell
docker exec -it csrit-backend sh
docker exec -it csrit-frontend sh
docker exec -it csrit-nginx sh

# Check network connectivity
docker exec csrit-nginx ping backend
docker exec csrit-backend ping postgres

# Check database connection
docker exec -it csrit-postgres psql -U postgres -d csrit_backend
```

### Performance Issues

```bash
# Check resource usage
docker stats

# Check disk space
df -h
docker system df

# Clean up unused Docker resources
docker system prune -a
docker volume prune
```

### Getting Help

1. **Check logs first**: `./scripts/deploy.sh logs dev`
2. **Verify configuration**: `./scripts/setup-env.sh status`
3. **Check our documentation**: Read `DEPLOYMENT.md` for detailed info
4. **Search issues**: Look for similar problems in project issues
5. **Ask for help**: Create an issue with:
   - What you were trying to do
   - What error you got
   - Your environment (OS, Docker version)
   - Relevant log output

## 📖 Additional Resources

- **[DEPLOYMENT.md](DEPLOYMENT.md)** - Detailed deployment guide
- **[NestJS Documentation](https://docs.nestjs.com/)** - Backend framework docs
- **[Vue.js Documentation](https://vuejs.org/)** - Frontend framework docs
- **[Docker Documentation](https://docs.docker.com/)** - Container platform docs
- **[nginx Documentation](https://nginx.org/en/docs/)** - Reverse proxy docs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Follow our code style guidelines
4. Write tests for new features
5. Commit changes: `git commit -m 'Add amazing feature'`
6. Push to branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Made with ❤️ for CSRIT Bank Sulsel**

*This README was written specifically for junior developers. If you have suggestions for improvement, please open an issue!*
